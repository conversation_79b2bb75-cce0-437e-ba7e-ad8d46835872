import React, { useEffect } from "react";
import styles from "./AccountPage.module.scss";
import useAuth from "../Auth/AuthActions";
import type { NotificationsProps } from "../Notifications/Notification";
import { useNavigate } from "react-router-dom";
import type { AuthStatusProps } from "../Auth/AuthTypes";
import Navigation from "../Navigation";
import Footer from "../Footer";

interface AccountPageProps extends NotificationsProps, AuthStatusProps {}

const AccountPage: React.FC<AccountPageProps> = ({
  notifications,
  setNotifications,
  setIsLoggedIn,
  isLoggedIn,
}) => {
  const navigate = useNavigate();
  const { getBearerToken, getRefreshToken, logout } = useAuth({
    setNotifications,
    notifications,
    setIsLoggedIn,
    isLoggedIn,
  });

  const handleLogout = async () => {
    await logout();
    navigate("/login");
  };

  useEffect(() => {
    let token = getBearerToken();
    if (token) {
      setIsLoggedIn?.(true);
    }
  }, []);

  useEffect(() => {
    if (!isLoggedIn) {
      navigate("/login");
      return;
    }
  }, [isLoggedIn]);

  return (
    <>
      <Navigation />
      <div className={styles.account}>
        <h1>Account Management</h1>
        <p>
          <strong>Access Token:</strong>{" "}
          <code>{getBearerToken() || "Not logged in"}</code>
        </p>
        <p>
          <strong>Refresh Token:</strong>{" "}
          <code>{getRefreshToken() || "Not available"}</code>
        </p>
        <button className={styles.logoutBtn} onClick={handleLogout}>
          Logout
        </button>
      </div>
      <Footer />
    </>
  );
};

export default AccountPage;
